import type { ReactNode } from 'react'


const AuthPageLayout = ({ children }: { children: ReactNode }) => {
  return (
    <main className="relative flex flex-col h-screen bg-white overflow-hidden">
      <div className="flex-1 brand-gradient rounded-b-2xl">
        {children}
      </div>
      <div className="flex-1"></div>
      <div className="absolute top-1/2 right-0 -translate-y-1/2 z-10">
        <img
          src="/authPageSvg.svg"
          alt="auth page svg"
          className="object-contain"
        />
      </div>
    </main>
  )
}

export default AuthPageLayout
